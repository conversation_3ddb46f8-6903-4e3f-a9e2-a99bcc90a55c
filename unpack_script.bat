@echo off
echo VMProtect 3.0.9 脱壳脚本
echo ========================

set INPUT_FILE=小T芙蓉王.exe
set OUTPUT_FILE=unpacked_小T芙蓉王.exe

echo 检查文件是否存在...
if not exist "%INPUT_FILE%" (
    echo 错误: 找不到目标文件 %INPUT_FILE%
    pause
    exit /b 1
)

echo 开始脱壳处理...
NoVmp.exe "%INPUT_FILE%" -o "%OUTPUT_FILE%" -v -r --fix-iat

if %ERRORLEVEL% EQU 0 (
    echo 脱壳完成！输出文件: %OUTPUT_FILE%
) else (
    echo 脱壳失败，错误代码: %ERRORLEVEL%
)

pause